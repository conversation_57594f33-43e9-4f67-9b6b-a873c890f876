<template>
  <el-dialog title="授权" :visible.sync="visible" width="400px !important" append-to-body>
    <div class="algorithm-list-container">

      <el-form ref="form" :model="rowData" label-width="80px">
        <el-form-item label="数据分级">
          <el-select v-model="rowData.value11" placeholder="请选择">
            <el-option v-for="(item, index) in classification" :key="index" :label="item.dictLabel" :value="item.dictValue">
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="数据分类">
          <el-select v-model="rowData.value10" placeholder="请选择">
            <el-option v-for="(item, index) in category" :key="index" :label="item.dictLabel" :value="item.dictValue">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="授权租户">
          <el-select v-model="rowData.value9" placeholder="请选择">
            <el-option v-for="(item, index) in resource" :key="index" :label="item.value1" :value="item.value1">
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="confirm">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import request from '@/utils/request.js'

export default {
  name: 'GradeClass',

  data() {
    return {
      visible: false,
      rowData: {
        value9: '',
        value10: '',
        value11: ''
      },
      classification: [],
      category: [],
      resource: []
    }
  },
  mounted() {
    Promise.all([
      this.getDicts('grades_lassified'),
      this.getDicts('classification')
    ]).then(([gradeResult, classResult, resourceResult]) => {
      this.classification = gradeResult.data
      this.category = classResult.data
      // 可以在这里添加后续需要执行的代码
    })
    request({ url: '/system/AutoOsmotic/list?pageNum=1&pageSize=10&type=tenant_permission_control' }).then(res => {
        this.resource = res.rows
    })
  },
  methods: {
    handleOpen(row) {
      this.rowData = row

      this.visible = true
    },
    async confirm() {
      try {
        const response = await request({
          url: '/system/AutoOsmotic',
          method: 'PUT',
          data: {
            ...this.rowData
          }
        })
        if (response.code === 200) {
          this.$modal.msgSuccess('修改成功')
          this.$emit('success')
          this.visible = false
        }
      } catch (error) {
        console.error('修改失败:', error)
        this.$message.error('修改失败，请重试')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.algorithm-list-container {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 10px;
}

.dialog-footer {
  text-align: right;
}
</style>
