<template>
  <div class="tenant-table">
    <EleSheet ref="sheetRef" v-bind="sheetProps" class="main-sheet">
      <template #before></template>

      <template #toolbar:after>
      </template>

      <!-- 租户信息列自定义渲染 -->
      <template #table:value1:simple="{ row }">
        <div class="tenant-info-cell">
          <div class="tenant-avatar">
            <i class="el-icon-office-building"></i>
          </div>
          <div class="tenant-details">
            <div class="tenant-name">{{ row.value1 }}</div>
            <div class="tenant-id">ID: {{ row.value2 }}</div>
          </div>
        </div>
      </template>

      <!-- 租户类型列自定义渲染 -->
      <template #table:value4:simple="{ row }">
        <el-tag :type="getTenantTypeStyle(row.value4)">
          {{ row.value4 }}
        </el-tag>
      </template>

      <!-- 权限等级列自定义渲染 -->
      <template #table:value5:simple="{ row }">
        <el-tag :type="getPermissionLevelStyle(row.value5)">
          {{ row.value5 }}
        </el-tag>
      </template>

      <!-- 权限状态列自定义渲染 -->
      <template #table:value7:simple="{ row }">
        <el-tag :type="getPermissionStatusStyle(row.value7)">
          {{ row.value7 }}
        </el-tag>
      </template>

      <!-- 状态列自定义渲染 -->
      <template #table:value8:simple="{ row }">
        <el-tag :type="getStatusStyle(row.value8)">
          {{ row.value8 }}
        </el-tag>
      </template>

      <!-- 操作列自定义渲染 -->
      <template #table:action:after="{ row }">
        <el-button type="text" size="mini" @click="handlePerm(row)">权限分配</el-button>
      </template>
      <template #info:before></template>
      <template #after></template>
    </EleSheet>

    <Permission ref="Assignment" @success="refreshTable"></Permission>
  </div>
</template>

<script>
import request from '@/utils/request.js'
import Permission from './Permission.vue'

export default {
  name: 'TenantTable',
  data() {
    return {
      tableType: 'tenant_permission_control'
    }
  },
  components: {
    Permission
  },
  computed: {
    sheetProps() {
      return {
        title: '租户管理',
        api: {
          list: (params) =>
            request({
              url: '/system/AutoOsmotic/list',
              method: 'get',
              params: {
                ...params,
                type: this.tableType
              }
            }),
          info: (id) =>
            request({
              url: `/system/AutoOsmotic/${id}`,
              method: 'get'
            }),
          add: (data) =>
            request({
              url: '/system/AutoOsmotic',
              method: 'post',
              data: {
                ...data,
                type: this.tableType
              }
            }),
          edit: (data) =>
            request({
              url: '/system/AutoOsmotic',
              method: 'put',
              data: {
                ...data,
                type: this.tableType
              }
            }),
          remove: (ids) =>
            request({
              url: `/system/AutoOsmotic/${ids}`,
              method: 'delete'
            })
        },
        model: {
          value1: {
            type: 'text',
            label: '租户信息',
            width: 250,
            fixed: 'left',
            search: { hidden: true },
            table: { sortable: false },
            form: { hidden: true }
          },
          value3: {
            type: 'text',
            label: '密钥',
            width: 150,
            fixed: 'left',
            search: { hidden: true },
            table: { sortable: false },
            form: { hidden: true }
          },
          tenantName: {
            type: 'text',
            label: '租户名称',
            width: 200,
            showOverflowTooltip: true,
            search: {
              type: 'input',
              placeholder: '请输入租户名称'
            },
            form: {
              type: 'input',
              rules: [
                { required: true, message: '请输入租户名称', trigger: 'blur' }
              ]
            },
            table: { hidden: true }
          },
          // description: {
          //   type: 'text',
          //   label: '描述',
          //   width: 150,
          //   showOverflowTooltip: true,
          //   search: { hidden: true },
          //   form: {
          //     type: 'textarea',
          //     placeholder: '请输入描述'
          //   }
          // },
          value4: {
            type: 'select',
            label: '租户类型',
            width: 120,
            search: {
              type: 'select',
              placeholder: '请选择租户类型',
              options: [
                { label: '企业租户', value: '企业租户' },
                { label: '政府租户', value: '政府租户' },
                { label: '教育机构', value: '教育机构' },
                { label: '个人租户', value: '个人租户' }
              ]
            },
            form: {
              type: 'select',
              options: [
                { label: '企业租户', value: '企业租户' },
                { label: '政府租户', value: '政府租户' },
                { label: '教育机构', value: '教育机构' },
                { label: '个人租户', value: '个人租户' }
              ],
              rules: [
                { required: true, message: '请选择租户类型', trigger: 'change' }
              ]
            }
          },
          value5: {
            type: 'select',
            label: '权限等级',
            width: 120,
            search: {
              hidden: true,
              type: 'select',
              placeholder: '请选择权限等级',
              options: [
                { label: '基础', value: '基础' },
                { label: '高级', value: '高级' },
                { label: '特殊', value: '特殊' }
              ]
            },
            form: {
              type: 'select',
              options: [
                { label: '基础', value: '基础' },
                { label: '高级', value: '高级' },
                { label: '特殊', value: '特殊' }
              ],
              rules: [
                { required: true, message: '请选择权限等级', trigger: 'change' }
              ]
            }
          },
          value6: {
            type: 'number',
            label: '可访问设备数',
            width: 140,
            search: { hidden: true }
            // form: {
            //   type: 'input-number',
            //   fieldProps: {
            //     min: 0,
            //     max: 99999
            //   }
            // }
          },
          value51: {
            type: 'datetime',
            label: '创建时间',
            width: 160,
            search: {
              type: 'date-range',
              placeholder: ['开始日期', '结束日期']
            },
            form: { hidden: true }
          },
          value7: {
            hidden: true,
            type: 'select',
            label: '权限等级',
            width: 120,
            search: {
              type: 'select',
              placeholder: '请选择权限状态',
              options: [
                { label: '管理员', value: '管理员' },
                { label: '普通用户', value: '普通用户' }
              ]
            },
            form: {
              type: 'select',
              options: [
                { label: '管理员', value: '管理员' },
                { label: '普通用户', value: '普通用户' }
              ]
            }
          },
          value8: {
            type: 'select',
            label: '状态',
            width: 100,
            search: {
              type: 'select',
              placeholder: '请选择状态',
              options: [
                { label: '已激活', value: '已激活' },
                { label: '已禁用', value: '已禁用' }
              ]
            },
            form: {
              type: 'select',
              options: [
                { label: '已激活', value: '已激活' },
                { label: '已禁用', value: '已禁用' }
              ]
            }
          }
        }
      }
    }
  },
  methods: {
    getTenantTypeStyle(type) {
      const typeMap = {
        '企业租户': 'primary',
        '政府租户': 'success',
        '教育机构': 'warning',
        '个人租户': 'info'
      }
      return typeMap[type] || 'info'
    },

    getPermissionLevelStyle(level) {
      const levelMap = {
        '基础': 'info',
        '高级': 'primary',
        '特殊': 'warning'
      }
      return levelMap[level] || 'info'
    },

    getPermissionStatusStyle(status) {
      const statusMap = {
        '管理员': 'danger',
        '普通用户': 'success'
      }
      return statusMap[status] || 'info'
    },

    getStatusStyle(status) {
      const statusMap = {
        '已激活': 'success',
        '已禁用': 'danger'
      }
      return statusMap[status] || 'info'
    },
    handlePerm(row) {
      this.$refs.Assignment.handleOpen(row)
    },
    handleAdd() {
      this.$refs.sheetRef.handleAdd()
    },

    handleView(row) {
      this.$refs.sheetRef.handleInfo(row)
    },

    handleEdit(row) {
      this.$refs.sheetRef.handleEdit(row)
    },

    handleConfig(row) {
      this.$message.info(`配置租户: ${row.tenantName}`)
      // 这里可以跳转到配置页面或打开配置弹窗
    },

    refreshTable() {
      this.$refs.sheetRef.getTableData()
    }
  }
}
</script>

<style scoped>
.tenant-table {
  height: 100%;
}

.main-sheet {
  height: 100%;
}

.tenant-info-cell {
  display: flex;
  align-items: center;
  gap: 12px;
}

.tenant-avatar {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background: linear-gradient(135deg, #409EFF, #67C23A);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 18px;
}

.tenant-details {
  flex: 1;
}

.tenant-name {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.tenant-id {
  font-size: 12px;
  color: #909399;
}
</style>
