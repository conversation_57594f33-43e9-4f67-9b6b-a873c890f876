<template>
  <div class="">
    <!-- 统计区域 -->
    <div class="stats-section">
      <el-row :gutter="20" class="stats-row">
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-content">
              <div class="stat-number">{{ Osmotic[0].count }}</div>
              <div class="stat-label">{{ Osmotic[0].name }}</div>
              <!-- <div class="stat-trend positive">较上月+1.25%</div> -->
            </div>
            <div class="stat-icon total-videos">
              <i class="el-icon-video-camera"></i>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-content">
              <div class="stat-number">{{ Osmotic[1].count }}</div>
              <div class="stat-label">{{ Osmotic[1].name }}</div>
              <!-- <div class="stat-trend">已覆盖全部视频</div> -->
            </div>
            <div class="stat-icon levels">
              <i class="el-icon-s-grid"></i>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-content">
              <div class="stat-number">{{ Osmotic[2].count }}</div>
              <div class="stat-label">{{ Osmotic[2].name }}</div>
              <!-- <div class="stat-trend positive">半月新增2</div> -->
            </div>
            <div class="stat-icon categories">
              <i class="el-icon-menu"></i>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-content">
              <div class="stat-number">{{ Osmotic[3].count }}</div>
              <div class="stat-label">{{ Osmotic[3].name }}</div>
              <!-- <div class="stat-trend warning">较昨日+8</div> -->
            </div>
            <div class="stat-icon pending">
              <i class="el-icon-warning"></i>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 主要内容区域 -->
    <EleSheet ref="sheetRef" v-bind="sheetProps" class="page-main main-sheet">
      <template #before></template>

      <template #toolbar:after>
      </template>

      <!-- 操作列自定义渲染 -->
      <template #table:hasAction:after="{ row }">
        <!-- <el-button type="text" size="mini" @click="handleGrade(row,1)">分级</el-button>
        <el-button type="text" size="mini" @click="handleGrade(row,2)">分类</el-button> -->
        <!-- <el-button type="text" icon="el-icon-edit" title="编辑" @click="handleEdit(row)">
        </el-button>
        <el-button type="text" icon="el-icon-delete" title="删除" @click="handleDelete(row)">
        </el-button> -->
      </template>
      <template #info:before></template>
      <template #after></template>
    </EleSheet>

    <GradeClass ref="GradeClass" @success="handlesuccess"></GradeClass>
  </div>
</template>

<script>
import request from '@/utils/request.js'
import { videoAssetLevel, videoAssetCategory, videoAssetStatus } from '@/dicts/video/index.js'
import GradeClass from '../assets/components/GradeClass.vue'
import { type } from 'windicss/utils'
export default {
  name: 'VideoAssetManagement',
  data() {
    return {
      tableType: 'video_data_desc',
      Osmotic: [{}, {}, {}, {}]
    }
  },
  components: {
    GradeClass
  },
  computed: {
    sheetProps() {
      return {
        title: '视频资产管理',
        api: {
          list: (params) =>
            request({
              url: '/system/AutoOsmotic/list',
              method: 'get',
              params: {
                ...params,
                type: this.tableType
              }
            }),
          info: (id) =>
            request({
              url: `/system/AutoOsmotic/${id}`,
              method: 'get'
            }),
          add: (data) =>
            request({
              url: '/system/AutoOsmotic',
              method: 'post',
              data: {
                ...data,
                type: this.tableType
              }
            }),
          edit: (data) =>
            request({
              url: '/system/AutoOsmotic',
              method: 'put',
              data: {
                ...data,
                type: this.tableType
              }
            }),
          remove: (ids) =>
            request({
              url: `/system/AutoOsmotic/${ids}`,
              method: 'delete'
            })
        },
        model: {
          // id: {
          //   type: 'text',
          //   label: '视频ID',
          //   width: 120,
          //   fixed: 'left',
          //   search: { hidden: true },
          //   form: { hidden: true }
          // },
          value1: {
            type: 'text',
            label: '名称',
            width: 150,
            fixed: 'left',
            table: { sortable: false },
            search: { placeholder: '请输入名称' },
            form: {
              type: 'input'
            }
          },
          value2: {
            type: 'text',
            label: '释义',
            width: 150,
            fixed: 'left',
            search: { hidden: true },
            table: { sortable: false },
            form: {
              type: 'input'
            }
          },
          value3: {
            type: 'text',
            label: '说明',
            width: 150,
            search: { hidden: true },
            form: {
              type: 'input'
            }
          },
          value4: {
            type: 'text',
            label: '值类型',
            width: 120,
            search: { hidden: true },
            form: {
              type: 'input'
            }
          },
          value5: {
            type: 'text',
            label: '路由库',
            width: 120,
            search: { hidden: true },
            form: {
              type: 'input'
            }
          },
          value6: {
            type: 'text',
            label: '路由表',
            width: 120,
            search: { hidden: true },
            form: {
              type: 'input'
            }
          },
          value7: {
            type: 'text',
            label: '存储类型',
            width: 100,
            search: { hidden: true },
            form: {
              type: 'input'
            }
          },
          value8: {
            type: 'text',
            label: '类型',
            width: 100,
            search: { hidden: true },
            form: {
              type: 'select',
              options: [
                { label: '基础标识信息', value: '基础标识信息' },
                { label: '技术属性信息', value: '技术属性信息' },
                { label: '内容描述信息', value: '内容描述信息' },
                { label: '权限信息', value: '权限信息' }
              ]
            }
          },
          value9: {
            type: 'text',
            label: '创始人',
            width: 160,
            search: { hidden: true },
            form: {
              hidden: true,
              type: 'date-picker',
              props: {
                type: 'datetime',
                placeholder: '请选择上传时间',
                format: 'yyyy-MM-dd HH:mm:ss',
                valueFormat: 'yyyy-MM-dd HH:mm:ss'
              }
            }
          },
          value10: {
            type: 'text',
            label: '创始时间',
            width: 100,
            search: { hidden: true },
            form: {
              hidden: true
            }
          }
          // uploadDateRange: {
          //   type: 'text',
          //   label: '上传日期',
          //   table: { hidden: true },
          //   search: {
          //     type: 'date-time-range',
          //     props: {
          //       type: 'daterange',
          //       rangeSeparator: '至',
          //       startPlaceholder: '开始日期',
          //       endPlaceholder: '结束日期',
          //       format: 'yyyy-MM-dd',
          //       valueFormat: 'yyyy-MM-dd'
          //     }
          //   },
          //   form: { hidden: true }
          // }
        }
      }
    }
  },
  mounted() {
    this.getData()
  },
  methods: {
    async getData() {
      try {
        const response = await request({
          url: '/system/AutoOsmotic/getVideoDataDescOverview',
          method: 'get'
        })
        this.Osmotic = response.data
      } catch (e) {
        console.log(e)
      }
    },
    getLevelType(level) {
      const levelMap = {
        '一级（公开）': 'success',
        '二级（内部）': 'primary',
        '三级（保密）': 'warning',
        '四级（机密）': 'danger',
        '五级（绝密）': 'danger'
      }
      return levelMap[level] || 'info'
    },
    getStatusType(status) {
      const statusMap = {
        '已激活': 'success',
        '未激活': 'info',
        '处理中': 'warning',
        '已过期': 'danger'
      }
      return statusMap[status] || 'info'
    },
    handlesuccess() {
      this.$refs.sheetRef.getTableData()
    },
    handleGrade(row, type) {
      this.$refs.GradeClass.handleOpen(row, type)
    },
    handleAdd() {
      this.$refs.sheetRef.handleAdd()
    },
    handleRefresh() {
      this.$refs.sheetRef.getTableData()
    },
    handleView(row) {
      this.$refs.sheetRef.handleInfo(row)
    },
    handleEdit(row) {
      this.$refs.sheetRef.handleEdit(row)
    },
    handleDelete(row) {
      this.$refs.sheetRef.handleRemove([row.id])
    }
  }
}
</script>

<style scoped>
.page-main {
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 统计区域样式 */
.stats-section {
  margin-bottom: 20px;
}

.stats-row {
  margin: 0;
}

.stat-card {
  display: flex;
  align-items: center;
  padding: 20px;
  background: #fff;
  border-radius: 8px;
  border: 1px solid #e8e8e8;
  transition: all 0.3s ease;
  box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.04);
}

.stat-card:hover {
  transform: translateY(-2px);
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 24px;
  color: #fff;
}

.stat-icon.total-videos {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon.levels {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-icon.categories {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-icon.pending {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 28px;
  font-weight: bold;
  color: #333;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 4px;
}

.stat-trend {
  font-size: 12px;
  color: #999;
}

.stat-trend.positive {
  color: #67c23a;
}

.stat-trend.warning {
  color: #e6a23c;
}

/* 主要内容区域 */
.main-sheet {
  flex: 1;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 视频信息单元格样式 */
.video-info-cell {
  display: flex;
  align-items: center;
  gap: 12px;
}

.video-thumbnail {
  width: 60px;
  height: 40px;
  border-radius: 4px;
  overflow: hidden;
  background: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
}

.video-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.video-details {
  flex: 1;
  min-width: 0;
}

.video-name {
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.video-id {
  font-size: 12px;
  color: #999;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .stats-row .el-col {
    margin-bottom: 16px;
  }
}

@media (max-width: 768px) {
  .stats-section {
    padding: 16px;
  }

  .stat-card {
    padding: 16px;
  }

  .stat-icon {
    width: 50px;
    height: 50px;
    font-size: 20px;
    margin-right: 12px;
  }

  .stat-number {
    font-size: 24px;
  }
}
</style>
