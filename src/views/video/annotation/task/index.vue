<template>
  <EleSheet ref="sheetRef" v-bind="sheetProps" class="page-main">
    <template #before></template>

    <!-- 工具栏自定义按钮 -->
    <template #toolbar:after>
    </template>

    <!-- 状态列自定义渲染 -->
    <!-- <template #table:value10:simple="{ row }">
      <el-tag :type="getStatusTagType(row.value10)">
        {{ row.value10 }}
      </el-tag>
    </template> -->

    <!-- 标注主体列自定义渲染 -->
    <template #table:value4:simple="{ row }">
      <el-tag type="primary">
        {{ row.value4 }}
      </el-tag>
    </template>

    <!-- 调度方式列自定义渲染 -->
    <template #table:value5:simple="{ row }">
      <el-tag type="info">
        {{ row.value5 }}
      </el-tag>
    </template>

    <template #form:value3:simple="{ model, field }">
      <VideoFileSelector
        v-model="model[field]"
        @change="handleVideoFileChange"
      />
    </template>

    <template #form:value7:simple="{ model }">
      <CronInput v-model="model.value7" />
    </template>

    <!-- 操作列自定义渲染 -->
    <template #table:action:after="{ row }">
      <el-button
        v-if="['待执行', '已停止'].includes(row.value10)"
        type="text"
        size="mini"
        @click="handleExecute(row)"
      >
        执行
      </el-button>
      <el-button
        v-if="['运行中'].includes(row.value10)"
        type="text"
        size="mini"
        @click="handleStop(row)"
      >
        停止
      </el-button>
      <el-button
        v-if="['异常', '已完成'].includes(row.value10)"
        type="text"
        size="mini"
        @click="handleReExecute(row)"
      >
        重新执行
      </el-button>
    </template>

    <template #info:before></template>
    <template #after></template>
  </EleSheet>
</template>

<script>
import request from '@/utils/request.js'
import { annotationTheme, scheduleStrategy, taskStatus, annotationTaskPriority, operatorCombinationType } from '@/dicts/video/index.js'
import CronInput from '@/components/CronInput/index.vue'
import VideoFileSelector from '@/components/VideoFileSelector/index.vue'
export default {
  name: 'VideoAnnotationTask',
  components: {
    CronInput,
    VideoFileSelector
  },
  data() {
    return {
      tableType: 'annotation_task_management',
      availableModels: [], // 可选择的模型列表（来自模型算子编排）
      annotationTaskPriorityDict: annotationTaskPriority
    }
  },
  computed: {
    sheetProps() {
      return {
        title: '视频标注任务',
        api: {
          list: (params) =>
            request({
              url: '/system/AutoOsmotic/list',
              method: 'get',
              params: {
                ...params,
                type: this.tableType
              }
            }),
          info: (id) =>
            request({
              url: `/system/AutoOsmotic/${id}`,
              method: 'get'
            }),
          add: (data) =>
            request({
              url: '/system/AutoOsmotic',
              method: 'post',
              data: {
                ...data,
                type: this.tableType
              }
            }),
          edit: (data) =>
            request({
              url: '/system/AutoOsmotic',
              method: 'put',
              data: {
                ...data,
                type: this.tableType
              }
            }),
          remove: (ids) =>
            request({
              url: `/system/AutoOsmotic/${ids}`,
              method: 'delete'
            })
        },

        model: {
          // 任务编号
          value1: {
            type: 'text',
            label: '任务编号',
            align: 'left',
            width: 120,
            search: {
              hidden: true
            },
            form: {
              hidden: true,
              rules: true
            }
          },
          // 任务名称
          value2: {
            type: 'text',
            label: '任务名称',
            align: 'left',
            width: 200,
            search: {
              placeholder: '请输入任务名称'
            },
            form: {
              rules: true
            }
          },
          // 视频唯一编号
          value3: {
            type: 'text',
            label: '视频唯一编号',
            align: 'left',
            width: 150,
            search: {
              placeholder: '请输入视频唯一编号'
            },
            form: {
              lg: 24,
              label: '选择视频'
            }
          },
          // 标注主体
          value4: {
            type: 'select',
            label: '标注主体',
            width: 120,
            search: {
              type: 'select',
              options: [
                { label: '全部主体', value: '' },
                ...annotationTheme
              ]
            },
            form: {
              value: annotationTheme[0].value
            },
            options: annotationTheme
          },
          // 调度方式
          value5: {
            type: 'select',
            label: '调度方式',
            width: 120,
            search: {
              type: 'select',
              options: [
                ...scheduleStrategy
              ]
            },
            form: {
              value: scheduleStrategy[0].value
            },
            options: scheduleStrategy
          },
          // 调度时间
          value6: {
            type: 'datetime',
            label: '调度时间',
            width: 160,
            search: {
              hidden: true
            },
            form: {
              hidden: true
            }
          },
          // CRON表达式
          value7: {
            type: 'text',
            label: 'CRON表达式',
            width: 150,
            search: {
              hidden: true
            },
            form: {
              hidden: (model) => {
                return model.value5 !== 'Cron表达式'
              }
            }
          },
          // 视频识别结果
          value8: {
            type: 'text',
            label: '视频识别结果',
            width: 200,
            search: {
              hidden: true
            },
            form: {
              hidden: true
            }
          },
          value9: {
            type: 'text',
            label: '关联模型算法',
            width: 200,
            search: {
              type: 'select',
              placeholder: '请选择关联模型算法',
              clearable: true,
              options: this.availableModels
            },
            form: {
              type: 'select',
              placeholder: '请选择关联模型算法',
              options: this.availableModels,
              rules: [
                { required: true, message: '请选择关联模型算法', trigger: 'change' }
              ],
              fieldProps: {
                multiple: true,
                class: 'min-w-full'
              },
              parameter: (value) => (value || []).join(','),
              formatter: (data) => {
                return (data.value9 || '').split(',')
              }
            },
            info: {
              formatter: (data) => data.value9
            },
            table: {
              tableColumnProps: {
                fixed: 'right'
              }
            }
          },
          // 状态
          value10: {
            type: 'select',
            label: '状态',
            width: 100,
            search: {
              type: 'select',
              options: [
                { label: '全部状态', value: '' },
                ...taskStatus
              ]
            },
            options: taskStatus,
            form: {
              hidden: true,
              type: 'select',
              placeholder: '请选择状态',
              options: taskStatus,
              rules: [
                { required: true, message: '请选择状态', trigger: 'change' }
              ],
              value: '待执行'
            },
            table: {
              tableColumnProps: {
                fixed: 'right'
              }
            }
          },
          value11: {
            type: 'text',
            label: '运行方式',
            width: 120,
            search: {
              type: 'select',
              placeholder: '请选择运行方式',
              clearable: true,
              options: operatorCombinationType
            },
            form: {
              type: 'select',
              placeholder: '请选择运行方式',
              options: operatorCombinationType,
              rules: [
                { required: true, message: '请选择运行方式', trigger: 'change' }
              ],
              value: '串联'
            }
          }
        }
      }
    }
  },
  async mounted() {
    await this.loadAvailableModels()
  },
  methods: {
    // 加载可用的模型列表（来自模型算子编排）
    async loadAvailableModels() {
      try {
        const response = await request({
          url: '/system/AutoOsmotic/list',
          method: 'get',
          params: {
            type: 'model_operator_orchestration',
            value7: '启用', // 仅加载启用状态的编排
            pageSize: 100
          }
        })

        if (response.code === 200 && response.rows && response.rows.length > 0) {
          this.availableModels = response.rows.map(item => ({
            label: item.value1, // 编排名称
            value: item.value1 // 编排ID
          }))
        } else {
          this.availableModels = []
        }
      } catch (error) {
        console.error('加载模型列表失败:', error)
        this.$message.error('加载模型列表失败')
        this.availableModels = []
      }
    },

    // 获取优先级标签类型
    getPriorityTagType(priority) {
      const priorityMap = {
        '高': 'danger',
        '中': 'warning',
        '低': 'info'
      }
      return priorityMap[priority] || 'info'
    },
    // 获取状态标签类型
    getStatusTagType(status) {
      const statusMap = {
        '运行中': 'success',
        '已完成': 'success',
        '已停止': 'info',
        '待执行': 'warning'
      }
      return statusMap[status] || 'info'
    },

    // 创建新任务
    handleCreateTask() {
      this.$refs.sheetRef.handleAdd()
    },

    // 执行任务
    async handleExecute(row) {
      try {
        await this.$modal.confirm(`确认要执行任务"${row.value2}"吗？`)

        const updateData = {
          ...row,
          value10: '运行中'
        }

        await this.sheetProps.api.edit(updateData)
        this.$modal.msgSuccess('任务已开始执行')
        // 刷新列表
        this.$refs.sheetRef.getTableData()
      } catch (error) {
        if (error !== 'cancel') {
          console.error('执行任务失败:', error)
          this.$modal.msgError('执行任务失败')
        }
      }
    },

    // 暂停任务
    async handlePause(row) {
      try {
        await this.$modal.confirm(`确认要暂停任务"${row.value2}"吗？`)

        const updateData = {
          ...row,
          value10: '已停止'
        }

        await this.sheetProps.api.edit(updateData)
        this.$modal.msgSuccess('任务已暂停')
        // 刷新列表
        this.$refs.sheetRef.getTableData()
      } catch (error) {
        if (error !== 'cancel') {
          console.error('暂停任务失败:', error)
          this.$modal.msgError('暂停任务失败')
        }
      }
    },

    // 停止任务
    async handleStop(row) {
      try {
        await this.$modal.confirm(`确认要停止任务"${row.value2}"吗？`)

        const updateData = {
          ...row,
          value10: '已停止'
        }

        await this.sheetProps.api.edit(updateData)
        this.$modal.msgSuccess('任务已停止')
        // 刷新列表
        this.$refs.sheetRef.getTableData()
      } catch (error) {
        if (error !== 'cancel') {
          console.error('停止任务失败:', error)
          this.$modal.msgError('停止任务失败')
        }
      }
    },

    // 重新执行任务
    async handleReExecute(row) {
      try {
        await this.$modal.confirm(`确认要重新执行任务"${row.value2}"吗？`)

        const updateData = {
          ...row,
          value10: '运行中'
        }

        await this.sheetProps.api.edit(updateData)
        this.$modal.msgSuccess('任务已重新开始执行')
        // 刷新列表
        this.$refs.sheetRef.getTableData()
      } catch (error) {
        if (error !== 'cancel') {
          console.error('重新执行任务失败:', error)
          this.$modal.msgError('重新执行任务失败')
        }
      }
    },

    // 查看详情
    handleViewDetails(row) {
      // 这里可以跳转到详情页面或打开详情弹窗
      console.log('查看任务详情:', row)
      this.$modal.msgInfo('查看任务详情功能待实现')
    }
  }
}
</script>

<style scoped>
.page-main {
  height: 100%;
}
</style>
